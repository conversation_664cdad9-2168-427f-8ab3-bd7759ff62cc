const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');

dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

/**
 * Generate personalized interview requirements based on candidate profile
 * @param {Object} candidateProfile - Basic candidate information
 * @param {string} jobRole - Target job role
 * @param {Array} skills - Required skills array
 * @param {string} resumeData - Complete resume data for context
 * @returns {Object} Generated requirements object
 */
exports.generatePersonalizedRequirements = async (candidateProfile, jobRole, skills, resumeData = '') => {
  try {
    const { candId, experience, companyType } = candidateProfile;

    // ============================================================================
    // DYNAMIC REQUIREMENT COUNT SYSTEM
    // ============================================================================
    // Instead of using random numbers, we let the AI generate requirements naturally
    // and then validate the count falls within our 7-10 range for balanced distribution
    // ============================================================================

    console.log(`🎯 DYNAMIC REQUIREMENTS: Generating requirements with AI-driven count (7-10 range validation)`);

    const prompt = `You are an expert technical recruiter creating personalized interview requirements.

CANDIDATE PROFILE:
- Role Applied For: ${jobRole}
- Required Skills: ${skills.join(', ')}
- Experience Level: ${experience}
- Company Type: ${companyType}
- Resume/Background: ${resumeData || 'Limited information available'}

Generate between 7-10 comprehensive requirements for evaluating this candidate.
Choose the optimal number based on role complexity and required skills coverage.
IMPORTANT: Generate the most appropriate number of requirements (7-10) for thorough evaluation.

Requirements should be:
1. Tailored to candidate's experience level (${experience})
2. Relevant to the role (${jobRole}) and company type (${companyType})
3. Assessable through interview responses
4. Progressive in difficulty
5. Cover technical skills, communication, and problem-solving
6. Provide comprehensive coverage of role expectations

Each requirement should be a SIMPLE PARAMETER NAME (2-4 words max), not a full sentence.
Examples: "JavaScript proficiency", "React knowledge", "API integration skills", "Problem solving", "Communication skills"

Return EXACTLY this JSON structure:
{
  "requirements": [
    {
      "id": "req_technical_1",
      "parameter": "JavaScript proficiency",
      "category": "technical",
      "priority": "high",
      "assessmentCriteria": [
        "Provides specific code examples or project details",
        "Explains technical concepts clearly",
        "Shows understanding of best practices"
      ],
      "experienceLevel": "${experience}",
      "satisfied": false,
      "satisfactionScore": 0,
      "evidence": []
    }
  ],
  "assessmentStrategy": {
    "completionThreshold": 0.75,
    "minRequiredSatisfied": 5,
    "focusAreas": ["Technical Skills", "Problem Solving", "Communication"]
  },
  "candidateContext": {
    "experienceLevel": "${experience}",
    "targetRole": "${jobRole}",
    "keySkills": ${JSON.stringify(skills)}
  }
}`;

    const result = await model.generateContent(prompt);
    const response = result.response.text();

    return parseAndValidateRequirements(response, candidateProfile, jobRole, skills);

  } catch (error) {
    console.error('Error generating requirements:', error);
    // Use dynamic fallback count selection for balanced distribution
    return createFallbackRequirements(jobRole, skills, candidateProfile.experience, null);
  }
};

/**
 * Parse and validate AI-generated requirements
 * @param {string} response - Raw AI response
 * @param {Object} candidateProfile - Candidate profile for validation
 * @param {string} jobRole - Job role for fallback
 * @param {Array} skills - Skills array for fallback
 * @returns {Object} Validated requirements object
 */
const parseAndValidateRequirements = (response, candidateProfile, jobRole, skills) => {
  try {
    // Clean the response to extract JSON
    let cleaned = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
    const parsed = JSON.parse(cleaned);

    // Validate and normalize requirements
    let validatedRequirements = parsed.requirements.map((req, index) => ({
      id: req.id || `req_${index + 1}`,
      parameter: String(req.parameter || req.description || ''), // Support both new 'parameter' and legacy 'description'
      category: req.category || 'technical',
      priority: req.priority || 'medium',
      assessmentCriteria: Array.isArray(req.assessmentCriteria) ? req.assessmentCriteria : [],
      experienceLevel: req.experienceLevel || candidateProfile.experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    }));

    // ============================================================================
    // DYNAMIC REQUIREMENT COUNT VALIDATION & BALANCED DISTRIBUTION
    // ============================================================================
    // Validate that AI-generated count falls within 7-10 range
    // If outside range, adjust to ensure balanced distribution
    // ============================================================================

    const aiGeneratedCount = validatedRequirements.length;
    console.log(`🤖 AI GENERATED COUNT: ${aiGeneratedCount} requirements`);

    // Validate count is within acceptable range (7-10)
    if (aiGeneratedCount < 7) {
      console.log(`⚠️ AI generated too few requirements (${aiGeneratedCount}). Padding to minimum of 7.`);
      // Add generic requirements to reach minimum
      const additionalReqs = createAdditionalRequirements(jobRole, skills, candidateProfile.experience, 7 - aiGeneratedCount);
      validatedRequirements = [...validatedRequirements, ...additionalReqs];
    } else if (aiGeneratedCount > 10) {
      console.log(`⚠️ AI generated too many requirements (${aiGeneratedCount}). Trimming to maximum of 10.`);
      // Keep the first 10 requirements (they're usually the most important)
      validatedRequirements = validatedRequirements.slice(0, 10);
    } else {
      console.log(`✅ AI generated optimal count: ${aiGeneratedCount} requirements (within 7-10 range)`);
    }

    const finalCount = validatedRequirements.length;
    console.log(`📊 FINAL REQUIREMENT COUNT: ${finalCount} (balanced distribution achieved)`);

    // Phase 3: Calculate dynamic minRequiredSatisfied based on actual requirement count
    const dynamicMinRequired = Math.max(5, Math.floor(finalCount * 0.7));

    // Log final dynamic count statistics
    console.log(`📈 DYNAMIC COUNT STATISTICS:`, {
      aiGenerated: aiGeneratedCount,
      finalCount: finalCount,
      withinRange: finalCount >= 7 && finalCount <= 10,
      adjustmentMade: aiGeneratedCount !== finalCount,
      dynamicMinRequired: dynamicMinRequired
    });

    return {
      requirements: validatedRequirements,
      assessmentStrategy: {
        completionThreshold: Number(parsed.assessmentStrategy?.completionThreshold) || 0.75,
        minRequiredSatisfied: Number(parsed.assessmentStrategy?.minRequiredSatisfied) || dynamicMinRequired,
        focusAreas: Array.isArray(parsed.assessmentStrategy?.focusAreas) ?
          parsed.assessmentStrategy.focusAreas : ['Technical Skills', 'Communication']
      },
      candidateContext: {
        experienceLevel: parsed.candidateContext?.experienceLevel || candidateProfile.experience,
        targetRole: parsed.candidateContext?.targetRole || jobRole,
        keySkills: Array.isArray(parsed.candidateContext?.keySkills) ?
          parsed.candidateContext.keySkills : skills
      },
      generatedAt: new Date().toISOString(),
      version: '1.0',
      dynamicCountInfo: {
        aiGeneratedCount: aiGeneratedCount,
        finalCount: finalCount,
        adjustmentMade: aiGeneratedCount !== finalCount,
        balancedDistribution: true
      }
    };

  } catch (error) {
    console.error('Error parsing requirements:', error);
    throw new Error(`Failed to parse AI requirements: ${error.message}`);
  }
};

/**
 * Create fallback requirements when AI generation fails
 * @param {string} jobRole - Job role
 * @param {Array} skills - Skills array
 * @param {string} experience - Experience level
 * @param {number} numRequirements - Number of requirements to generate (passed from main function)
 * @returns {Object} Fallback requirements object
 */
/**
 * Creates additional requirements when AI generates too few
 * @param {string} jobRole - Job role
 * @param {Array} skills - Skills array
 * @param {string} experience - Experience level
 * @param {number} count - Number of additional requirements needed
 * @returns {Array} Array of additional requirements
 */
const createAdditionalRequirements = (jobRole, skills, experience, count) => {
  const additionalReqs = [];
  const baseTemplates = [
    { parameter: "Problem solving skills", category: "cognitive", priority: "high" },
    { parameter: "Team collaboration", category: "communication", priority: "medium" },
    { parameter: "Code quality standards", category: "technical", priority: "high" },
    { parameter: "Learning adaptability", category: "cognitive", priority: "medium" },
    { parameter: "Project management", category: "organizational", priority: "medium" },
    { parameter: "Technical documentation", category: "communication", priority: "medium" },
    { parameter: "Debugging proficiency", category: "technical", priority: "high" }
  ];

  for (let i = 0; i < count && i < baseTemplates.length; i++) {
    const template = baseTemplates[i];
    additionalReqs.push({
      id: `req_additional_${i + 1}`,
      parameter: template.parameter,
      category: template.category,
      priority: template.priority,
      assessmentCriteria: [
        "Demonstrates practical application",
        "Shows understanding of best practices",
        "Provides relevant examples"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    });
  }

  return additionalReqs;
};

const createFallbackRequirements = (jobRole, skills, experience, numRequirements = null) => {
  // ============================================================================
  // BALANCED DISTRIBUTION FALLBACK SYSTEM
  // ============================================================================
  // Instead of random generation, use a balanced approach that cycles through
  // the 7-10 range to ensure equal distribution over time
  // ============================================================================

  let targetRequirements;
  if (numRequirements) {
    targetRequirements = numRequirements;
    console.log(`🎯 FALLBACK REQUIREMENTS: Using specified count ${targetRequirements}`);
  } else {
    // Use a time-based balanced distribution instead of random
    const now = new Date();
    const seed = now.getHours() + now.getMinutes() + now.getSeconds();
    targetRequirements = 7 + (seed % 4); // Cycles through 7, 8, 9, 10 based on time
    console.log(`⚖️ BALANCED FALLBACK: Generated ${targetRequirements} requirements (time-based distribution, seed: ${seed})`);
    console.log(`📊 DISTRIBUTION TRACKING: This ensures equal probability across 7-10 range over time`);
  }

  const baseRequirements = [
    {
      id: "req_technical_1",
      parameter: `${skills[0] || 'Technical'} proficiency`,
      category: "technical",
      priority: "high",
      assessmentCriteria: [
        "Shows practical knowledge of required technologies",
        "Provides specific examples from experience",
        "Explains technical concepts clearly"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_communication_1",
      parameter: "Communication skills",
      category: "communication",
      priority: "high",
      assessmentCriteria: [
        "Explains ideas clearly and concisely",
        "Uses appropriate technical terminology",
        "Responds effectively to follow-up questions"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_problem_solving_1",
      parameter: "Problem solving",
      category: "problem_solving",
      priority: "high",
      assessmentCriteria: [
        "Describes systematic approach to problem-solving",
        "Shows logical thinking process",
        "Demonstrates ability to break down complex problems"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_technical_2",
      parameter: `${skills[1] || 'Framework'} knowledge`,
      category: "technical",
      priority: "high",
      assessmentCriteria: [
        "Demonstrates understanding of framework concepts",
        "Shows practical implementation experience",
        "Explains best practices and patterns"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_behavioral_1",
      parameter: "Team collaboration",
      category: "behavioral",
      priority: "medium",
      assessmentCriteria: [
        "Describes effective teamwork experiences",
        "Shows ability to work with diverse teams",
        "Demonstrates conflict resolution skills"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_technical_3",
      parameter: "Code quality practices",
      category: "technical",
      priority: "medium",
      assessmentCriteria: [
        "Understands clean code principles",
        "Shows knowledge of testing practices",
        "Demonstrates code review experience"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_behavioral_2",
      parameter: "Learning adaptability",
      category: "behavioral",
      priority: "medium",
      assessmentCriteria: [
        "Shows willingness to learn new technologies",
        "Demonstrates ability to adapt to change",
        "Provides examples of continuous learning"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_technical_4",
      parameter: "System design thinking",
      category: "technical",
      priority: "medium",
      assessmentCriteria: [
        "Shows understanding of system architecture",
        "Demonstrates scalability considerations",
        "Explains design trade-offs"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_behavioral_3",
      parameter: "Project ownership",
      category: "behavioral",
      priority: "low",
      assessmentCriteria: [
        "Takes responsibility for project outcomes",
        "Shows initiative in problem-solving",
        "Demonstrates accountability"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_technical_5",
      parameter: "Performance optimization",
      category: "technical",
      priority: "low",
      assessmentCriteria: [
        "Understands performance bottlenecks",
        "Shows optimization techniques knowledge",
        "Demonstrates monitoring and debugging skills"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    }
  ];

  // Return the first targetRequirements from the base requirements
  const fallbackRequirements = baseRequirements.slice(0, targetRequirements);

  return {
    requirements: fallbackRequirements,
    assessmentStrategy: {
      completionThreshold: 0.75,
      minRequiredSatisfied: Math.max(5, Math.floor(targetRequirements * 0.7)), // Dynamic based on requirement count
      focusAreas: ["Technical Skills", "Communication", "Problem Solving"]
    },
    candidateContext: {
      experienceLevel: experience,
      targetRole: jobRole,
      keySkills: skills
    },
    generatedAt: new Date().toISOString(),
    version: '1.0',
    fallback: true
  };
};
